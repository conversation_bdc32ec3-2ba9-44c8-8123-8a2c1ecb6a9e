import { PostService } from './post-service.js';
import { createSuccessResponse, createPaginatedResponse } from '../../core/utils/helpers.js';
import { asyncHandler } from '../../core/utils/errors.js';

/**
 * Post controller
 */
export class PostController {
  constructor() {
    this.postService = new PostService();
  }

  /**
   * Get all posts with pagination and search
   * GET /posts
   */
  getAllPosts = asyncHandler(async (request, reply) => {
    const result = await this.postService.getAllPosts(request.query);
    
    reply.send(createPaginatedResponse(
      result.posts, 
      result.pagination, 
      'Posts retrieved successfully'
    ));
  });

  /**
   * Get post by ID
   * GET /posts/:id
   */
  getPostById = asyncHandler(async (request, reply) => {
    const post = await this.postService.getPostById(request.params.id);
    
    reply.send(createSuccessResponse(post, 'Post retrieved successfully'));
  });

  /**
   * Create new post
   * POST /posts
   */
  createPost = asyncHandler(async (request, reply) => {
    const post = await this.postService.createPost(request.body, request.user.id);
    
    reply.code(201).send(createSuccessResponse(post, 'Post created successfully'));
  });

  /**
   * Update post by ID
   * PUT /posts/:id
   */
  updatePost = asyncHandler(async (request, reply) => {
    const post = await this.postService.updatePost(
      request.params.id, 
      request.body, 
      request.user.id
    );
    
    reply.send(createSuccessResponse(post, 'Post updated successfully'));
  });

  /**
   * Delete post by ID
   * DELETE /posts/:id
   */
  deletePost = asyncHandler(async (request, reply) => {
    const result = await this.postService.deletePost(request.params.id, request.user.id);
    
    reply.send(createSuccessResponse(result));
  });

  /**
   * Get posts by author
   * GET /posts/author/:authorId
   */
  getPostsByAuthor = asyncHandler(async (request, reply) => {
    const result = await this.postService.getPostsByAuthor(
      request.params.authorId, 
      request.query
    );
    
    reply.send(createPaginatedResponse(
      result.posts, 
      result.pagination, 
      'Author posts retrieved successfully'
    ));
  });

  /**
   * Get current user's posts
   * GET /posts/me
   */
  getUserPosts = asyncHandler(async (request, reply) => {
    const result = await this.postService.getUserPosts(request.user.id, request.query);
    
    reply.send(createPaginatedResponse(
      result.posts, 
      result.pagination, 
      'Your posts retrieved successfully'
    ));
  });

  /**
   * Search posts
   * GET /posts/search
   */
  searchPosts = asyncHandler(async (request, reply) => {
    const { q: searchTerm } = request.query;
    
    if (!searchTerm) {
      return reply.code(400).send({
        success: false,
        error: {
          message: 'Search term is required',
          code: 'VALIDATION_ERROR',
          statusCode: 400,
        }
      });
    }

    const result = await this.postService.searchPosts(searchTerm, request.query);
    
    reply.send(createPaginatedResponse(
      result.posts, 
      result.pagination, 
      `Search results for "${searchTerm}"`
    ));
  });

  /**
   * Get post statistics
   * GET /posts/stats
   */
  getPostStats = asyncHandler(async (request, reply) => {
    const stats = await this.postService.getPostStats();
    
    reply.send(createSuccessResponse(stats, 'Post statistics retrieved successfully'));
  });

  /**
   * Get current user's post statistics
   * GET /posts/me/stats
   */
  getUserPostStats = asyncHandler(async (request, reply) => {
    const stats = await this.postService.getPostStats(request.user.id);
    
    reply.send(createSuccessResponse(stats, 'Your post statistics retrieved successfully'));
  });

  /**
   * Publish/unpublish post
   * PATCH /posts/:id/publish
   */
  togglePublishPost = asyncHandler(async (request, reply) => {
    const { published } = request.body;
    
    const post = await this.postService.updatePost(
      request.params.id,
      { isPublished: published },
      request.user.id
    );
    
    const message = published ? 'Post published successfully' : 'Post unpublished successfully';
    reply.send(createSuccessResponse(post, message));
  });
}
