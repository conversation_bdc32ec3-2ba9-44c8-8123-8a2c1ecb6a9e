# Test Environment Configuration
NODE_ENV=test
PORT=3001
HOST=127.0.0.1

# Test Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=fus_app_test_db
DB_CONNECTION_LIMIT=5

# JWT Configuration for Testing
JWT_SECRET=test-jwt-secret-key
JWT_EXPIRES_IN=1h
JWT_ISSUER=fus-app-api-test
JWT_AUDIENCE=fus-app-client-test

# Security Configuration for Testing
BCRYPT_ROUNDS=4

# Rate Limiting for Testing (more permissive)
RATE_LIMIT_MAX=10000
RATE_LIMIT_WINDOW=1 minute

# CORS Configuration for Testing
CORS_ORIGIN=http://localhost:3001
CORS_CREDENTIALS=true

# Pagination Configuration
DEFAULT_PAGE_LIMIT=10
MAX_PAGE_LIMIT=100

# Frontend URL for Testing
FRONTEND_URL=http://localhost:3001
