import bcrypt from 'bcryptjs';
import { nanoid } from 'nanoid';
import { config } from '../config/index.js';

/**
 * Hash password using bcrypt
 * @param {string} password - Plain text password
 * @returns {Promise<string>} Hashed password
 */
export async function hashPassword(password) {
  const saltRounds = config.security.bcryptRounds;
  return bcrypt.hash(password, saltRounds);
}

/**
 * Compare password with hash
 * @param {string} password - Plain text password
 * @param {string} hash - Hashed password
 * @returns {Promise<boolean>} Comparison result
 */
export async function comparePassword(password, hash) {
  return bcrypt.compare(password, hash);
}

/**
 * Generate secure random token
 * @param {number} length - Token length (default: 32)
 * @returns {string} Random token
 */
export function generateToken(length = 32) {
  return nanoid(length);
}

/**
 * Generate reset password token with expiration
 * @returns {Object} Token and expiration date
 */
export function generateResetToken() {
  const token = generateToken(64);
  const expiresAt = new Date(Date.now() + config.security.resetTokenExpiry);
  
  return { token, expiresAt };
}

/**
 * Create success response
 * @param {*} data - Response data
 * @param {string} message - Success message
 * @returns {Object} Success response
 */
export function createSuccessResponse(data, message = null) {
  return {
    success: true,
    data,
    ...(message && { message }),
  };
}

/**
 * Create paginated response
 * @param {Array} items - Data items
 * @param {Object} pagination - Pagination metadata
 * @param {string} message - Success message
 * @returns {Object} Paginated response
 */
export function createPaginatedResponse(items, pagination, message = null) {
  return {
    success: true,
    data: items,
    pagination,
    ...(message && { message }),
  };
}

/**
 * Sanitize user data for public display
 * @param {Object} user - User object
 * @returns {Object} Sanitized user data
 */
export function sanitizeUser(user) {
  if (!user) return null;
  
  const {
    password,
    resetPasswordToken,
    resetPasswordExpires,
    ...sanitizedUser
  } = user;
  
  return sanitizedUser;
}

/**
 * Sanitize user data for public profile
 * @param {Object} user - User object
 * @returns {Object} Public user profile
 */
export function sanitizeUserPublic(user) {
  if (!user) return null;
  
  return {
    id: user.id,
    username: user.username,
    firstName: user.firstName,
    lastName: user.lastName,
    bio: user.bio,
    avatar: user.avatar,
    createdAt: user.createdAt,
  };
}

/**
 * Generate unique slug from title
 * @param {string} title - Original title
 * @param {Function} checkExists - Function to check if slug exists
 * @returns {Promise<string>} Unique slug
 */
export async function generateUniqueSlug(title, checkExists) {
  let baseSlug = title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
  
  let slug = baseSlug;
  let counter = 1;
  
  while (await checkExists(slug)) {
    slug = `${baseSlug}-${counter}`;
    counter++;
  }
  
  return slug;
}

/**
 * Parse pagination parameters
 * @param {Object} query - Query parameters
 * @returns {Object} Parsed pagination options
 */
export function parsePaginationParams(query) {
  const page = Math.max(1, parseInt(query.page) || 1);
  const limit = Math.min(
    config.pagination.maxLimit,
    Math.max(1, parseInt(query.limit) || config.pagination.defaultLimit)
  );
  
  return { page, limit };
}

/**
 * Parse sort parameters
 * @param {Object} query - Query parameters
 * @param {Array} allowedFields - Allowed sort fields
 * @returns {Object} Parsed sort options
 */
export function parseSortParams(query, allowedFields = ['createdAt']) {
  const sortBy = allowedFields.includes(query.sortBy) ? query.sortBy : allowedFields[0];
  const sortOrder = ['asc', 'desc'].includes(query.sortOrder) ? query.sortOrder : 'desc';
  
  return { sortBy, sortOrder };
}

/**
 * Validate email format
 * @param {string} email - Email address
 * @returns {boolean} Validation result
 */
export function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate username format
 * @param {string} username - Username
 * @returns {boolean} Validation result
 */
export function isValidUsername(username) {
  const usernameRegex = /^[a-zA-Z0-9_]{3,30}$/;
  return usernameRegex.test(username);
}

/**
 * Validate password strength
 * @param {string} password - Password
 * @returns {Object} Validation result with details
 */
export function validatePasswordStrength(password) {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  const isValid = password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers;
  
  return {
    isValid,
    details: {
      minLength: password.length >= minLength,
      hasUpperCase,
      hasLowerCase,
      hasNumbers,
      hasSpecialChar,
    },
  };
}

/**
 * Sleep for specified milliseconds
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise} Promise that resolves after delay
 */
export function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry function with exponential backoff
 * @param {Function} fn - Function to retry
 * @param {number} maxRetries - Maximum retry attempts
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {Promise} Function result
 */
export async function retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000) {
  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      const delay = baseDelay * Math.pow(2, attempt);
      await sleep(delay);
    }
  }
}
