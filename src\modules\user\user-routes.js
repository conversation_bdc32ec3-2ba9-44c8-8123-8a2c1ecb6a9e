import { AuthController } from '../auth/auth-controller.js';
import { requireAuth } from '../../core/middleware/auth.js';
import { schemas, responseSchemas } from '../../core/utils/validation.js';

/**
 * User routes plugin
 * @param {Object} fastify - Fastify instance
 * @param {Object} options - Plugin options
 */
export async function userRoutes(fastify, options) {
  const authController = new AuthController();

  // Get public user profile by username
  fastify.get('/users/:username', {
    schema: {
      description: 'Get public user profile by username',
      tags: ['Users'],
      params: schemas.usernameParam.params,
      response: {
        200: {
          description: 'User profile retrieved successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: responseSchemas.userPublic
          }
        },
        404: responseSchemas.error,
      }
    }
  }, authController.getPublicUserProfile);

  // Get current user profile (requires authentication)
  fastify.get('/users/me', {
    preHandler: [requireAuth],
    schema: {
      description: 'Get current user profile',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          description: 'Current user profile retrieved successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: responseSchemas.userPrivate
          }
        },
        401: responseSchemas.error,
        404: responseSchemas.error,
      }
    }
  }, authController.getCurrentUser);

  // Update current user profile (requires authentication)
  fastify.put('/users/me', {
    preHandler: [requireAuth],
    schema: {
      description: 'Update current user profile',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      body: schemas.userUpdate.body,
      response: {
        200: {
          description: 'User profile updated successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: responseSchemas.userPrivate,
            message: { type: 'string' }
          }
        },
        400: responseSchemas.error,
        401: responseSchemas.error,
        404: responseSchemas.error,
      }
    }
  }, authController.updateCurrentUser);
}
