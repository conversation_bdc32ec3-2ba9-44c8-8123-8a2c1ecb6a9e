import { mysqlTable, varchar, text, timestamp, int, boolean, index } from 'drizzle-orm/mysql-core';
import { sql } from 'drizzle-orm';

/**
 * Users table schema
 */
export const users = mysqlTable('users', {
  id: int('id').primaryKey().autoincrement(),
  username: varchar('username', { length: 50 }).notNull().unique(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  password: varchar('password', { length: 255 }).notNull(),
  firstName: varchar('first_name', { length: 100 }),
  lastName: varchar('last_name', { length: 100 }),
  bio: text('bio'),
  avatar: varchar('avatar', { length: 500 }),
  isActive: boolean('is_active').default(true),
  emailVerified: boolean('email_verified').default(false),
  resetPasswordToken: varchar('reset_password_token', { length: 255 }),
  resetPasswordExpires: timestamp('reset_password_expires'),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
}, (table) => ({
  usernameIdx: index('username_idx').on(table.username),
  emailIdx: index('email_idx').on(table.email),
  resetTokenIdx: index('reset_token_idx').on(table.resetPasswordToken),
}));

/**
 * Posts table schema
 */
export const posts = mysqlTable('posts', {
  id: int('id').primaryKey().autoincrement(),
  title: varchar('title', { length: 255 }).notNull(),
  content: text('content').notNull(),
  excerpt: text('excerpt'),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  authorId: int('author_id').notNull(),
  isPublished: boolean('is_published').default(false),
  publishedAt: timestamp('published_at'),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`),
  updatedAt: timestamp('updated_at').default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`),
}, (table) => ({
  authorIdx: index('author_idx').on(table.authorId),
  slugIdx: index('slug_idx').on(table.slug),
  publishedIdx: index('published_idx').on(table.isPublished),
  createdAtIdx: index('created_at_idx').on(table.createdAt),
}));

/**
 * JWT Blacklist table for logout functionality
 */
export const jwtBlacklist = mysqlTable('jwt_blacklist', {
  id: int('id').primaryKey().autoincrement(),
  token: text('token').notNull(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').default(sql`CURRENT_TIMESTAMP`),
}, (table) => ({
  tokenIdx: index('token_idx').on(table.token),
  expiresIdx: index('expires_idx').on(table.expiresAt),
}));
