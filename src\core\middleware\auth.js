import { AuthenticationError, AuthorizationError } from '../utils/errors.js';
import { JwtBlacklistRepository } from '../database/repositories/jwt-blacklist-repository.js';
import { UserRepository } from '../database/repositories/user-repository.js';

const jwtBlacklistRepo = new JwtBlacklistRepository();
const userRepo = new UserRepository();

/**
 * JWT Authentication middleware
 * Verifies JWT token and adds user to request context
 */
export async function authenticate(request, reply) {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('Missing or invalid authorization header');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    if (!token) {
      throw new AuthenticationError('Missing JWT token');
    }

    // Check if token is blacklisted
    const isBlacklisted = await jwtBlacklistRepo.isTokenBlacklisted(token);
    if (isBlacklisted) {
      throw new AuthenticationError('Token has been invalidated');
    }

    // Verify JWT token
    let decoded;
    try {
      decoded = await request.jwtVerify();
    } catch (error) {
      if (error.code === 'FAST_JWT_INVALID_TOKEN') {
        throw new AuthenticationError('Invalid JWT token');
      }
      if (error.code === 'FAST_JWT_EXPIRED') {
        throw new AuthenticationError('JWT token has expired');
      }
      throw new AuthenticationError('JWT verification failed');
    }

    // Get user from database
    const user = await userRepo.findById(decoded.userId);
    if (!user) {
      throw new AuthenticationError('User not found');
    }

    if (!user.isActive) {
      throw new AuthenticationError('User account is deactivated');
    }

    // Add user and token to request context
    request.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      isActive: user.isActive,
      emailVerified: user.emailVerified,
    };
    request.token = token;

  } catch (error) {
    if (error instanceof AuthenticationError) {
      throw error;
    }
    throw new AuthenticationError('Authentication failed');
  }
}

/**
 * Optional authentication middleware
 * Adds user to request context if token is provided and valid
 */
export async function optionalAuthenticate(request, reply) {
  try {
    await authenticate(request, reply);
  } catch (error) {
    // Ignore authentication errors for optional auth
    request.user = null;
    request.token = null;
  }
}

/**
 * Authorization middleware factory
 * Creates middleware that checks if user has required permissions
 * @param {Function} checkPermission - Function to check user permissions
 * @returns {Function} Authorization middleware
 */
export function authorize(checkPermission) {
  return async (request, reply) => {
    // Ensure user is authenticated first
    if (!request.user) {
      throw new AuthenticationError('Authentication required');
    }

    // Check permissions
    const hasPermission = await checkPermission(request.user, request);
    
    if (!hasPermission) {
      throw new AuthorizationError('Insufficient permissions');
    }
  };
}

/**
 * Check if user owns the resource
 * @param {Object} user - Current user
 * @param {Object} request - Request object
 * @returns {boolean} Ownership status
 */
export function checkOwnership(user, request) {
  // This can be customized based on the resource type
  const resourceUserId = request.params.userId || request.body.userId;
  return user.id === parseInt(resourceUserId);
}

/**
 * Check if user is admin (placeholder for future role-based auth)
 * @param {Object} user - Current user
 * @returns {boolean} Admin status
 */
export function checkAdmin(user) {
  // Placeholder for role-based authorization
  // In a real app, you'd check user.role === 'admin' or similar
  return false;
}

/**
 * Middleware to require authentication
 */
export const requireAuth = authenticate;

/**
 * Middleware to require admin privileges
 */
export const requireAdmin = authorize(checkAdmin);

/**
 * Middleware to require resource ownership
 */
export const requireOwnership = authorize(checkOwnership);

/**
 * Generate JWT payload for user
 * @param {Object} user - User object
 * @returns {Object} JWT payload
 */
export function generateJwtPayload(user) {
  return {
    userId: user.id,
    username: user.username,
    email: user.email,
    iat: Math.floor(Date.now() / 1000),
  };
}

/**
 * Extract token from request
 * @param {Object} request - Fastify request object
 * @returns {string|null} JWT token or null
 */
export function extractToken(request) {
  const authHeader = request.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  return authHeader.substring(7);
}

/**
 * Get token expiration date
 * @param {string} token - JWT token
 * @returns {Date|null} Expiration date or null
 */
export function getTokenExpiration(token) {
  try {
    // Decode token without verification to get expiration
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
    
    if (!payload.exp) return null;
    
    return new Date(payload.exp * 1000);
  } catch (error) {
    return null;
  }
}
