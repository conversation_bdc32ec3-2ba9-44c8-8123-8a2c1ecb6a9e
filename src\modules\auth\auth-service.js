import { UserRepository } from '../../core/database/repositories/user-repository.js';
import { JwtBlacklistRepository } from '../../core/database/repositories/jwt-blacklist-repository.js';
import { emailService } from '../../services/email-service.js';
import { 
  hashPassword, 
  comparePassword, 
  generateResetToken,
  sanitizeUser,
  generateUniqueSlug
} from '../../core/utils/helpers.js';
import { 
  ValidationError, 
  AuthenticationError, 
  ConflictError, 
  NotFoundError 
} from '../../core/utils/errors.js';
import { generateJwtPayload, getTokenExpiration } from '../../core/middleware/auth.js';

/**
 * Authentication service
 */
export class AuthService {
  constructor() {
    this.userRepo = new UserRepository();
    this.jwtBlacklistRepo = new JwtBlacklistRepository();
  }

  /**
   * Register a new user
   * @param {Object} userData - User registration data
   * @param {Object} fastify - Fastify instance for JWT signing
   * @returns {Promise<Object>} Registration result
   */
  async register(userData, fastify) {
    const { username, email, password, firstName, lastName } = userData;

    // Check if user already exists
    const existingUserByEmail = await this.userRepo.findByEmail(email);
    if (existingUserByEmail) {
      throw new ConflictError('Email already registered');
    }

    const existingUserByUsername = await this.userRepo.findByUsername(username);
    if (existingUserByUsername) {
      throw new ConflictError('Username already taken');
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create user
    const newUser = await this.userRepo.create({
      username,
      email,
      password: hashedPassword,
      firstName: firstName || null,
      lastName: lastName || null,
      isActive: true,
      emailVerified: false,
    });

    // Generate JWT token
    const jwtPayload = generateJwtPayload(newUser);
    const token = await fastify.jwt.sign(jwtPayload);

    // Send welcome email (optional, don't fail registration if email fails)
    try {
      await emailService.sendWelcomeEmail(email, username);
    } catch (error) {
      console.warn('Failed to send welcome email:', error.message);
    }

    return {
      user: sanitizeUser(newUser),
      token,
    };
  }

  /**
   * Login user
   * @param {Object} loginData - Login credentials
   * @param {Object} fastify - Fastify instance for JWT signing
   * @returns {Promise<Object>} Login result
   */
  async login(loginData, fastify) {
    const { identifier, password } = loginData;

    // Find user by email or username
    let user = await this.userRepo.findByEmail(identifier);
    if (!user) {
      user = await this.userRepo.findByUsername(identifier);
    }

    if (!user) {
      throw new AuthenticationError('Invalid credentials');
    }

    // Check if user is active
    if (!user.isActive) {
      throw new AuthenticationError('Account is deactivated');
    }

    // Verify password
    const isPasswordValid = await comparePassword(password, user.password);
    if (!isPasswordValid) {
      throw new AuthenticationError('Invalid credentials');
    }

    // Generate JWT token
    const jwtPayload = generateJwtPayload(user);
    const token = await fastify.jwt.sign(jwtPayload);

    return {
      user: sanitizeUser(user),
      token,
    };
  }

  /**
   * Initiate password reset
   * @param {string} email - User email
   * @returns {Promise<Object>} Reset initiation result
   */
  async forgotPassword(email) {
    const user = await this.userRepo.findByEmail(email);
    if (!user) {
      // Don't reveal if email exists or not for security
      return {
        message: 'If the email exists, a password reset link has been sent',
      };
    }

    // Generate reset token
    const { token, expiresAt } = generateResetToken();

    // Save reset token to database
    await this.userRepo.updateResetToken(email, token, expiresAt);

    // Send reset email
    try {
      await emailService.sendPasswordResetEmail(email, token, user.username);
    } catch (error) {
      console.error('Failed to send password reset email:', error.message);
      throw new Error('Failed to send password reset email');
    }

    return {
      message: 'Password reset link has been sent to your email',
    };
  }

  /**
   * Reset password with token
   * @param {Object} resetData - Reset password data
   * @returns {Promise<Object>} Reset result
   */
  async resetPassword(resetData) {
    const { token, password } = resetData;

    // Find user by reset token
    const user = await this.userRepo.findByResetToken(token);
    if (!user) {
      throw new ValidationError('Invalid or expired reset token');
    }

    // Check if token is expired
    if (user.resetPasswordExpires < new Date()) {
      throw new ValidationError('Reset token has expired');
    }

    // Hash new password
    const hashedPassword = await hashPassword(password);

    // Update user password and clear reset token
    await this.userRepo.updateById(user.id, {
      password: hashedPassword,
    });
    await this.userRepo.clearResetToken(user.id);

    return {
      message: 'Password has been reset successfully',
    };
  }

  /**
   * Logout user (blacklist JWT token)
   * @param {string} token - JWT token to blacklist
   * @returns {Promise<Object>} Logout result
   */
  async logout(token) {
    // Get token expiration
    const expiresAt = getTokenExpiration(token);
    if (!expiresAt) {
      throw new ValidationError('Invalid token format');
    }

    // Add token to blacklist
    const success = await this.jwtBlacklistRepo.addToken(token, expiresAt);
    if (!success) {
      throw new Error('Failed to logout');
    }

    return {
      message: 'Logged out successfully',
    };
  }

  /**
   * Get current user profile
   * @param {number} userId - User ID
   * @returns {Promise<Object>} User profile
   */
  async getCurrentUser(userId) {
    const user = await this.userRepo.findById(userId);
    if (!user) {
      throw new NotFoundError('User');
    }

    return sanitizeUser(user);
  }

  /**
   * Update current user profile
   * @param {number} userId - User ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated user profile
   */
  async updateCurrentUser(userId, updateData) {
    const user = await this.userRepo.findById(userId);
    if (!user) {
      throw new NotFoundError('User');
    }

    // Filter allowed update fields
    const allowedFields = ['firstName', 'lastName', 'bio', 'avatar'];
    const filteredData = {};
    
    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    }

    if (Object.keys(filteredData).length === 0) {
      throw new ValidationError('No valid fields to update');
    }

    const updatedUser = await this.userRepo.updateById(userId, filteredData);
    return sanitizeUser(updatedUser);
  }

  /**
   * Get public user profile
   * @param {string} username - Username
   * @returns {Promise<Object>} Public user profile
   */
  async getPublicUserProfile(username) {
    const user = await this.userRepo.getPublicProfile(username);
    if (!user) {
      throw new NotFoundError('User');
    }

    return user;
  }

  /**
   * Change user password
   * @param {number} userId - User ID
   * @param {Object} passwordData - Password change data
   * @returns {Promise<Object>} Change result
   */
  async changePassword(userId, passwordData) {
    const { currentPassword, newPassword } = passwordData;

    const user = await this.userRepo.findById(userId);
    if (!user) {
      throw new NotFoundError('User');
    }

    // Verify current password
    const isCurrentPasswordValid = await comparePassword(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new AuthenticationError('Current password is incorrect');
    }

    // Hash new password
    const hashedNewPassword = await hashPassword(newPassword);

    // Update password
    await this.userRepo.updateById(userId, {
      password: hashedNewPassword,
    });

    return {
      message: 'Password changed successfully',
    };
  }

  /**
   * Deactivate user account
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Deactivation result
   */
  async deactivateAccount(userId) {
    const user = await this.userRepo.findById(userId);
    if (!user) {
      throw new NotFoundError('User');
    }

    await this.userRepo.updateById(userId, {
      isActive: false,
    });

    return {
      message: 'Account deactivated successfully',
    };
  }
}
