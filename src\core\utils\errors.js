/**
 * Base application error class
 */
export class AppError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR') {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }

  toJSON() {
    return {
      error: {
        message: this.message,
        code: this.code,
        statusCode: this.statusCode,
      }
    };
  }
}

/**
 * Validation error class
 */
export class ValidationError extends AppError {
  constructor(message, details = null) {
    super(message, 400, 'VALIDATION_ERROR');
    this.details = details;
  }

  toJSON() {
    return {
      error: {
        message: this.message,
        code: this.code,
        statusCode: this.statusCode,
        details: this.details,
      }
    };
  }
}

/**
 * Authentication error class
 */
export class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

/**
 * Authorization error class
 */
export class AuthorizationError extends AppError {
  constructor(message = 'Access denied') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

/**
 * Not found error class
 */
export class NotFoundError extends AppError {
  constructor(resource = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND');
  }
}

/**
 * Conflict error class
 */
export class ConflictError extends AppError {
  constructor(message = 'Resource already exists') {
    super(message, 409, 'CONFLICT_ERROR');
  }
}

/**
 * Rate limit error class
 */
export class RateLimitError extends AppError {
  constructor(message = 'Too many requests') {
    super(message, 429, 'RATE_LIMIT_ERROR');
  }
}

/**
 * Database error class
 */
export class DatabaseError extends AppError {
  constructor(message = 'Database operation failed') {
    super(message, 500, 'DATABASE_ERROR');
  }
}

/**
 * External service error class
 */
export class ExternalServiceError extends AppError {
  constructor(service, message = 'External service error') {
    super(`${service}: ${message}`, 502, 'EXTERNAL_SERVICE_ERROR');
    this.service = service;
  }
}

/**
 * Create error response object
 * @param {Error} error - Error instance
 * @param {Object} request - Fastify request object
 * @returns {Object} Error response
 */
export function createErrorResponse(error, request = null) {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  // Handle operational errors
  if (error.isOperational) {
    return {
      success: false,
      ...error.toJSON(),
      ...(isDevelopment && { stack: error.stack }),
      ...(request && { 
        timestamp: new Date().toISOString(),
        path: request.url,
        method: request.method,
      }),
    };
  }

  // Handle non-operational errors
  const statusCode = error.statusCode || 500;
  const message = isDevelopment ? error.message : 'Internal server error';
  
  return {
    success: false,
    error: {
      message,
      code: 'INTERNAL_ERROR',
      statusCode,
    },
    ...(isDevelopment && { stack: error.stack }),
    ...(request && { 
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
    }),
  };
}

/**
 * Handle async errors in route handlers
 * @param {Function} fn - Async function
 * @returns {Function} Wrapped function
 */
export function asyncHandler(fn) {
  return async (request, reply) => {
    try {
      await fn(request, reply);
    } catch (error) {
      reply.code(error.statusCode || 500).send(createErrorResponse(error, request));
    }
  };
}

/**
 * Global error handler for Fastify
 * @param {Error} error - Error instance
 * @param {Object} request - Fastify request object
 * @param {Object} reply - Fastify reply object
 */
export function globalErrorHandler(error, request, reply) {
  // Log error
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (isDevelopment) {
    console.error('Error:', error);
  } else {
    // In production, log only operational errors or use proper logging service
    if (error.isOperational) {
      console.error(`Operational Error: ${error.message}`);
    } else {
      console.error(`System Error: ${error.message}`);
    }
  }

  const errorResponse = createErrorResponse(error, request);
  const statusCode = error.statusCode || 500;

  reply.code(statusCode).send(errorResponse);
}
