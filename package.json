{"name": "fus-app-be-v2", "version": "1.0.0", "description": "REST API backend using Fastify framework", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "node --watch src/server.js", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["fastify", "api", "rest", "nodejs", "drizzle"], "author": "", "license": "MIT", "dependencies": {"fastify": "^4.24.3", "@fastify/cors": "^8.4.0", "@fastify/helmet": "^11.1.1", "@fastify/rate-limit": "^9.0.1", "@fastify/jwt": "^7.2.4", "drizzle-orm": "^0.29.0", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "mailgun.js": "^9.4.1", "form-data": "^4.0.0", "nanoid": "^5.0.4", "dotenv": "^16.3.1", "@sinclair/typebox": "^0.31.28"}, "devDependencies": {"drizzle-kit": "^0.20.4", "jest": "^29.7.0", "@types/jest": "^29.5.8", "nodemon": "^3.0.2"}}