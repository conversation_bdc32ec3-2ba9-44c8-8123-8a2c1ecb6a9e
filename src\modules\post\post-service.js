import { PostRepository } from '../../core/database/repositories/post-repository.js';
import { 
  generateUniqueSlug,
  parsePaginationParams,
  parseSortParams,
  createSlug
} from '../../core/utils/helpers.js';
import { 
  ValidationError, 
  NotFoundError,
  AuthorizationError
} from '../../core/utils/errors.js';

/**
 * Post service
 */
export class PostService {
  constructor() {
    this.postRepo = new PostRepository();
  }

  /**
   * Create a new post
   * @param {Object} postData - Post data
   * @param {number} authorId - Author user ID
   * @returns {Promise<Object>} Created post
   */
  async createPost(postData, authorId) {
    const { title, content, excerpt, isPublished = false } = postData;

    // Generate unique slug from title
    const slug = await generateUniqueSlug(
      title,
      async (slug) => {
        const existing = await this.postRepo.findBySlug(slug);
        return !!existing;
      }
    );

    // Generate excerpt if not provided
    const finalExcerpt = excerpt || this.generateExcerpt(content);

    const newPost = await this.postRepo.create({
      title,
      content,
      excerpt: finalExcerpt,
      slug,
      authorId,
      isPublished,
      publishedAt: isPublished ? new Date() : null,
    });

    return newPost;
  }

  /**
   * Get all posts with pagination and search
   * @param {Object} queryParams - Query parameters
   * @returns {Promise<Object>} Posts with pagination
   */
  async getAllPosts(queryParams) {
    const { page, limit } = parsePaginationParams(queryParams);
    const { sortBy, sortOrder } = parseSortParams(queryParams, [
      'createdAt', 'updatedAt', 'title', 'publishedAt'
    ]);

    const options = {
      page,
      limit,
      search: queryParams.search || '',
      sortBy,
      sortOrder,
      published: queryParams.published !== undefined ? queryParams.published : null,
    };

    return this.postRepo.findAll(options);
  }

  /**
   * Get post by ID
   * @param {number} postId - Post ID
   * @returns {Promise<Object>} Post with author information
   */
  async getPostById(postId) {
    const post = await this.postRepo.findById(postId);
    if (!post) {
      throw new NotFoundError('Post');
    }

    return post;
  }

  /**
   * Update post by ID
   * @param {number} postId - Post ID
   * @param {Object} updateData - Update data
   * @param {number} userId - Current user ID
   * @returns {Promise<Object>} Updated post
   */
  async updatePost(postId, updateData, userId) {
    // Check if post exists
    const existingPost = await this.postRepo.findById(postId);
    if (!existingPost) {
      throw new NotFoundError('Post');
    }

    // Check ownership
    if (!await this.postRepo.isOwner(postId, userId)) {
      throw new AuthorizationError('You can only edit your own posts');
    }

    const { title, content, excerpt, isPublished } = updateData;
    const updateFields = {};

    // Update title and regenerate slug if title changed
    if (title && title !== existingPost.title) {
      updateFields.title = title;
      updateFields.slug = await generateUniqueSlug(
        title,
        async (slug) => {
          // Exclude current post from slug check
          const existing = await this.postRepo.findBySlug(slug);
          return existing && existing.id !== postId;
        }
      );
    }

    // Update content
    if (content !== undefined) {
      updateFields.content = content;
    }

    // Update excerpt
    if (excerpt !== undefined) {
      updateFields.excerpt = excerpt;
    } else if (content && content !== existingPost.content) {
      // Auto-generate excerpt if content changed but excerpt not provided
      updateFields.excerpt = this.generateExcerpt(content);
    }

    // Update published status
    if (isPublished !== undefined && isPublished !== existingPost.isPublished) {
      updateFields.isPublished = isPublished;
      
      if (isPublished && !existingPost.publishedAt) {
        updateFields.publishedAt = new Date();
      } else if (!isPublished) {
        updateFields.publishedAt = null;
      }
    }

    if (Object.keys(updateFields).length === 0) {
      throw new ValidationError('No valid fields to update');
    }

    return this.postRepo.updateById(postId, updateFields);
  }

  /**
   * Delete post by ID
   * @param {number} postId - Post ID
   * @param {number} userId - Current user ID
   * @returns {Promise<Object>} Deletion result
   */
  async deletePost(postId, userId) {
    // Check if post exists
    const existingPost = await this.postRepo.findById(postId);
    if (!existingPost) {
      throw new NotFoundError('Post');
    }

    // Check ownership
    if (!await this.postRepo.isOwner(postId, userId)) {
      throw new AuthorizationError('You can only delete your own posts');
    }

    const success = await this.postRepo.deleteById(postId);
    if (!success) {
      throw new Error('Failed to delete post');
    }

    return {
      message: 'Post deleted successfully',
    };
  }

  /**
   * Get posts by author
   * @param {number} authorId - Author ID
   * @param {Object} queryParams - Query parameters
   * @returns {Promise<Object>} Author's posts with pagination
   */
  async getPostsByAuthor(authorId, queryParams) {
    const { page, limit } = parsePaginationParams(queryParams);
    const { sortBy, sortOrder } = parseSortParams(queryParams, [
      'createdAt', 'updatedAt', 'title', 'publishedAt'
    ]);

    const options = {
      page,
      limit,
      search: queryParams.search || '',
      sortBy,
      sortOrder,
      authorId,
      published: queryParams.published !== undefined ? queryParams.published : null,
    };

    return this.postRepo.findAll(options);
  }

  /**
   * Get user's own posts (including unpublished)
   * @param {number} userId - User ID
   * @param {Object} queryParams - Query parameters
   * @returns {Promise<Object>} User's posts with pagination
   */
  async getUserPosts(userId, queryParams) {
    const { page, limit } = parsePaginationParams(queryParams);
    const { sortBy, sortOrder } = parseSortParams(queryParams, [
      'createdAt', 'updatedAt', 'title', 'publishedAt'
    ]);

    const options = {
      page,
      limit,
      search: queryParams.search || '',
      sortBy,
      sortOrder,
      authorId: userId,
      // Don't filter by published status for user's own posts
    };

    return this.postRepo.findAll(options);
  }

  /**
   * Search posts
   * @param {string} searchTerm - Search term
   * @param {Object} queryParams - Query parameters
   * @returns {Promise<Object>} Search results with pagination
   */
  async searchPosts(searchTerm, queryParams) {
    const { page, limit } = parsePaginationParams(queryParams);
    const { sortBy, sortOrder } = parseSortParams(queryParams, [
      'createdAt', 'updatedAt', 'title', 'publishedAt'
    ]);

    const options = {
      page,
      limit,
      search: searchTerm,
      sortBy,
      sortOrder,
      published: true, // Only search published posts
    };

    return this.postRepo.findAll(options);
  }

  /**
   * Generate excerpt from content
   * @param {string} content - Post content
   * @param {number} maxLength - Maximum excerpt length
   * @returns {string} Generated excerpt
   */
  generateExcerpt(content, maxLength = 200) {
    if (!content) return '';
    
    // Remove HTML tags if present
    const plainText = content.replace(/<[^>]*>/g, '');
    
    if (plainText.length <= maxLength) {
      return plainText;
    }
    
    // Find the last complete word within the limit
    const truncated = plainText.substring(0, maxLength);
    const lastSpaceIndex = truncated.lastIndexOf(' ');
    
    if (lastSpaceIndex > 0) {
      return truncated.substring(0, lastSpaceIndex) + '...';
    }
    
    return truncated + '...';
  }

  /**
   * Check if user can edit post
   * @param {number} postId - Post ID
   * @param {number} userId - User ID
   * @returns {Promise<boolean>} Edit permission
   */
  async canEditPost(postId, userId) {
    return this.postRepo.isOwner(postId, userId);
  }

  /**
   * Check if user can delete post
   * @param {number} postId - Post ID
   * @param {number} userId - User ID
   * @returns {Promise<boolean>} Delete permission
   */
  async canDeletePost(postId, userId) {
    return this.postRepo.isOwner(postId, userId);
  }

  /**
   * Get post statistics
   * @param {number} authorId - Author ID (optional)
   * @returns {Promise<Object>} Post statistics
   */
  async getPostStats(authorId = null) {
    const options = authorId ? { authorId } : {};
    
    const publishedResult = await this.postRepo.findAll({ 
      ...options, 
      published: true, 
      limit: 1 
    });
    
    const draftResult = await this.postRepo.findAll({ 
      ...options, 
      published: false, 
      limit: 1 
    });
    
    const totalResult = await this.postRepo.findAll({ 
      ...options, 
      limit: 1 
    });

    return {
      total: totalResult.pagination.totalItems,
      published: publishedResult.pagination.totalItems,
      drafts: draftResult.pagination.totalItems,
    };
  }
}
