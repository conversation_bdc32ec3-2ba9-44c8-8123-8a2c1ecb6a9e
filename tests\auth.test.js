import { buildApp } from '../src/server.js';

describe('Authentication Endpoints', () => {
  let app;

  beforeAll(async () => {
    app = buildApp({ logger: false });
    await app.ready();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /auth/register', () => {
    test('should register a new user successfully', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'TestPass123',
        firstName: 'Test',
        lastName: 'User'
      };

      const response = await app.inject({
        method: 'POST',
        url: '/auth/register',
        payload: userData
      });

      expect(response.statusCode).toBe(201);
      
      const result = JSON.parse(response.payload);
      expect(result.success).toBe(true);
      expect(result.data.user.username).toBe(userData.username);
      expect(result.data.user.email).toBe(userData.email);
      expect(result.data.token).toBeDefined();
      expect(result.data.user.password).toBeUndefined(); // Password should not be returned
    });

    test('should return validation error for invalid email', async () => {
      const userData = {
        username: 'testuser2',
        email: 'invalid-email',
        password: 'TestPass123'
      };

      const response = await app.inject({
        method: 'POST',
        url: '/auth/register',
        payload: userData
      });

      expect(response.statusCode).toBe(400);
    });

    test('should return validation error for weak password', async () => {
      const userData = {
        username: 'testuser3',
        email: '<EMAIL>',
        password: '123' // Too weak
      };

      const response = await app.inject({
        method: 'POST',
        url: '/auth/register',
        payload: userData
      });

      expect(response.statusCode).toBe(400);
    });
  });

  describe('POST /auth/login', () => {
    test('should login with valid credentials', async () => {
      // First register a user
      const userData = {
        username: 'logintest',
        email: '<EMAIL>',
        password: 'TestPass123'
      };

      await app.inject({
        method: 'POST',
        url: '/auth/register',
        payload: userData
      });

      // Then login
      const loginData = {
        identifier: 'logintest',
        password: 'TestPass123'
      };

      const response = await app.inject({
        method: 'POST',
        url: '/auth/login',
        payload: loginData
      });

      expect(response.statusCode).toBe(200);
      
      const result = JSON.parse(response.payload);
      expect(result.success).toBe(true);
      expect(result.data.user.username).toBe(userData.username);
      expect(result.data.token).toBeDefined();
    });

    test('should return error for invalid credentials', async () => {
      const loginData = {
        identifier: 'nonexistent',
        password: 'WrongPass123'
      };

      const response = await app.inject({
        method: 'POST',
        url: '/auth/login',
        payload: loginData
      });

      expect(response.statusCode).toBe(401);
    });
  });
});

describe('Health Check', () => {
  let app;

  beforeAll(async () => {
    app = buildApp({ logger: false });
    await app.ready();
  });

  afterAll(async () => {
    await app.close();
  });

  test('should return health status', async () => {
    const response = await app.inject({
      method: 'GET',
      url: '/health'
    });

    expect(response.statusCode).toBe(200);
    
    const result = JSON.parse(response.payload);
    expect(result.status).toBeDefined();
    expect(result.services).toBeDefined();
    expect(result.services.database).toBeDefined();
    expect(result.services.email).toBeDefined();
  });
});
