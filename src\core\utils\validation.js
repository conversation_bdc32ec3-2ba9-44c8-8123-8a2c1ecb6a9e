import { Type } from '@sinclair/typebox';

/**
 * Common validation patterns
 */
export const patterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  username: /^[a-zA-Z0-9_]{3,30}$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
};

/**
 * Common validation schemas
 */
export const schemas = {
  // User schemas
  userRegister: {
    body: Type.Object({
      username: Type.String({ 
        minLength: 3, 
        maxLength: 30,
        pattern: patterns.username.source,
        description: 'Username must be 3-30 characters, alphanumeric and underscore only'
      }),
      email: Type.String({ 
        format: 'email',
        maxLength: 255,
        description: 'Valid email address'
      }),
      password: Type.String({ 
        minLength: 8,
        maxLength: 128,
        pattern: patterns.password.source,
        description: 'Password must be at least 8 characters with uppercase, lowercase, and number'
      }),
      firstName: Type.Optional(Type.String({ maxLength: 100 })),
      lastName: Type.Optional(Type.String({ maxLength: 100 })),
    }),
  },

  userLogin: {
    body: Type.Object({
      identifier: Type.String({ 
        minLength: 3,
        maxLength: 255,
        description: 'Username or email'
      }),
      password: Type.String({ 
        minLength: 1,
        maxLength: 128
      }),
    }),
  },

  userUpdate: {
    body: Type.Object({
      firstName: Type.Optional(Type.String({ maxLength: 100 })),
      lastName: Type.Optional(Type.String({ maxLength: 100 })),
      bio: Type.Optional(Type.String({ maxLength: 1000 })),
      avatar: Type.Optional(Type.String({ maxLength: 500, format: 'uri' })),
    }),
  },

  forgotPassword: {
    body: Type.Object({
      email: Type.String({ 
        format: 'email',
        maxLength: 255
      }),
    }),
  },

  resetPassword: {
    body: Type.Object({
      token: Type.String({ 
        minLength: 1,
        maxLength: 255
      }),
      password: Type.String({ 
        minLength: 8,
        maxLength: 128,
        pattern: patterns.password.source,
        description: 'Password must be at least 8 characters with uppercase, lowercase, and number'
      }),
    }),
  },

  // Post schemas
  postCreate: {
    body: Type.Object({
      title: Type.String({ 
        minLength: 1,
        maxLength: 255
      }),
      content: Type.String({ 
        minLength: 1,
        maxLength: 50000
      }),
      excerpt: Type.Optional(Type.String({ maxLength: 500 })),
      isPublished: Type.Optional(Type.Boolean({ default: false })),
    }),
  },

  postUpdate: {
    body: Type.Object({
      title: Type.Optional(Type.String({ 
        minLength: 1,
        maxLength: 255
      })),
      content: Type.Optional(Type.String({ 
        minLength: 1,
        maxLength: 50000
      })),
      excerpt: Type.Optional(Type.String({ maxLength: 500 })),
      isPublished: Type.Optional(Type.Boolean()),
    }),
  },

  // Query parameter schemas
  postQuery: {
    querystring: Type.Object({
      page: Type.Optional(Type.Integer({ minimum: 1, default: 1 })),
      limit: Type.Optional(Type.Integer({ minimum: 1, maximum: 100, default: 10 })),
      search: Type.Optional(Type.String({ maxLength: 255 })),
      sortBy: Type.Optional(Type.Union([
        Type.Literal('createdAt'),
        Type.Literal('updatedAt'),
        Type.Literal('title'),
        Type.Literal('publishedAt')
      ], { default: 'createdAt' })),
      sortOrder: Type.Optional(Type.Union([
        Type.Literal('asc'),
        Type.Literal('desc')
      ], { default: 'desc' })),
      published: Type.Optional(Type.Boolean()),
    }),
  },

  // Parameter schemas
  idParam: {
    params: Type.Object({
      id: Type.Integer({ minimum: 1 }),
    }),
  },

  usernameParam: {
    params: Type.Object({
      username: Type.String({ 
        minLength: 3,
        maxLength: 30,
        pattern: patterns.username.source
      }),
    }),
  },
};

/**
 * Response schemas
 */
export const responseSchemas = {
  success: Type.Object({
    success: Type.Literal(true),
    data: Type.Any(),
    message: Type.Optional(Type.String()),
  }),

  error: Type.Object({
    success: Type.Literal(false),
    error: Type.Object({
      message: Type.String(),
      code: Type.String(),
      statusCode: Type.Integer(),
      details: Type.Optional(Type.Any()),
    }),
    timestamp: Type.Optional(Type.String()),
    path: Type.Optional(Type.String()),
    method: Type.Optional(Type.String()),
  }),

  pagination: Type.Object({
    currentPage: Type.Integer(),
    totalPages: Type.Integer(),
    totalItems: Type.Integer(),
    itemsPerPage: Type.Integer(),
    hasNextPage: Type.Boolean(),
    hasPrevPage: Type.Boolean(),
  }),

  userPublic: Type.Object({
    id: Type.Integer(),
    username: Type.String(),
    firstName: Type.Union([Type.String(), Type.Null()]),
    lastName: Type.Union([Type.String(), Type.Null()]),
    bio: Type.Union([Type.String(), Type.Null()]),
    avatar: Type.Union([Type.String(), Type.Null()]),
    createdAt: Type.String(),
  }),

  userPrivate: Type.Object({
    id: Type.Integer(),
    username: Type.String(),
    email: Type.String(),
    firstName: Type.Union([Type.String(), Type.Null()]),
    lastName: Type.Union([Type.String(), Type.Null()]),
    bio: Type.Union([Type.String(), Type.Null()]),
    avatar: Type.Union([Type.String(), Type.Null()]),
    isActive: Type.Boolean(),
    emailVerified: Type.Boolean(),
    createdAt: Type.String(),
    updatedAt: Type.String(),
  }),

  post: Type.Object({
    id: Type.Integer(),
    title: Type.String(),
    content: Type.String(),
    excerpt: Type.Union([Type.String(), Type.Null()]),
    slug: Type.String(),
    authorId: Type.Integer(),
    isPublished: Type.Boolean(),
    publishedAt: Type.Union([Type.String(), Type.Null()]),
    createdAt: Type.String(),
    updatedAt: Type.String(),
    author: Type.Object({
      id: Type.Integer(),
      username: Type.String(),
      firstName: Type.Union([Type.String(), Type.Null()]),
      lastName: Type.Union([Type.String(), Type.Null()]),
      avatar: Type.Union([Type.String(), Type.Null()]),
    }),
  }),
};

/**
 * Validate data against schema
 * @param {Object} data - Data to validate
 * @param {Object} schema - JSON schema
 * @returns {Object} Validation result
 */
export function validateData(data, schema) {
  // This would typically use a JSON schema validator
  // For now, we'll rely on Fastify's built-in validation
  return { valid: true, errors: [] };
}

/**
 * Create slug from title
 * @param {string} title - Post title
 * @returns {string} URL-friendly slug
 */
export function createSlug(title) {
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}
