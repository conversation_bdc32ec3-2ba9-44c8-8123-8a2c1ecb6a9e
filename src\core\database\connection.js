import mysql from 'mysql2/promise';
import { drizzle } from 'drizzle-orm/mysql2';
import * as schema from './schema.js';

/**
 * Database connection configuration
 */
const connectionConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'fus_app_db',
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 10,
  acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000,
  timeout: parseInt(process.env.DB_TIMEOUT) || 60000,
};

/**
 * Create MySQL connection pool
 */
const pool = mysql.createPool(connectionConfig);

/**
 * Drizzle database instance
 */
export const db = drizzle(pool, { 
  schema, 
  mode: 'default',
  logger: process.env.NODE_ENV === 'development'
});

/**
 * Test database connection
 * @returns {Promise<boolean>} Connection status
 */
export async function testConnection() {
  try {
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    return true;
  } catch (error) {
    console.error('Database connection failed:', error.message);
    return false;
  }
}

/**
 * Close database connection pool
 * @returns {Promise<void>}
 */
export async function closeConnection() {
  try {
    await pool.end();
    console.log('Database connection pool closed');
  } catch (error) {
    console.error('Error closing database connection:', error.message);
  }
}

/**
 * Database health check
 * @returns {Promise<Object>} Health status
 */
export async function healthCheck() {
  try {
    const connection = await pool.getConnection();
    const [rows] = await connection.execute('SELECT 1 as status');
    connection.release();
    
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: connectionConfig.database,
      host: connectionConfig.host,
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
    };
  }
}
