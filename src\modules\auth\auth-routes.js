import { AuthController } from './auth-controller.js';
import { requireAuth } from '../../core/middleware/auth.js';
import { schemas, responseSchemas } from '../../core/utils/validation.js';

/**
 * Auth routes plugin
 * @param {Object} fastify - Fastify instance
 * @param {Object} options - Plugin options
 */
export async function authRoutes(fastify, options) {
  const authController = new AuthController();

  // Register user
  fastify.post('/auth/register', {
    schema: {
      description: 'Register a new user',
      tags: ['Authentication'],
      body: schemas.userRegister.body,
      response: {
        201: {
          description: 'User registered successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                user: responseSchemas.userPrivate,
                token: { type: 'string' }
              }
            },
            message: { type: 'string' }
          }
        },
        400: responseSchemas.error,
        409: responseSchemas.error,
      }
    }
  }, authController.register);

  // Login user
  fastify.post('/auth/login', {
    schema: {
      description: 'Login user',
      tags: ['Authentication'],
      body: schemas.userLogin.body,
      response: {
        200: {
          description: 'Login successful',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                user: responseSchemas.userPrivate,
                token: { type: 'string' }
              }
            },
            message: { type: 'string' }
          }
        },
        401: responseSchemas.error,
      }
    }
  }, authController.login);

  // Forgot password
  fastify.post('/auth/forgot-password', {
    schema: {
      description: 'Initiate password reset',
      tags: ['Authentication'],
      body: schemas.forgotPassword.body,
      response: {
        200: {
          description: 'Password reset initiated',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' }
              }
            }
          }
        },
        500: responseSchemas.error,
      }
    }
  }, authController.forgotPassword);

  // Reset password
  fastify.post('/auth/reset-password', {
    schema: {
      description: 'Reset password with token',
      tags: ['Authentication'],
      body: schemas.resetPassword.body,
      response: {
        200: {
          description: 'Password reset successful',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' }
              }
            }
          }
        },
        400: responseSchemas.error,
      }
    }
  }, authController.resetPassword);

  // Logout user (requires authentication)
  fastify.post('/auth/logout', {
    preHandler: [requireAuth],
    schema: {
      description: 'Logout user',
      tags: ['Authentication'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          description: 'Logout successful',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' }
              }
            }
          }
        },
        401: responseSchemas.error,
      }
    }
  }, authController.logout);

  // Change password (requires authentication)
  fastify.post('/auth/change-password', {
    preHandler: [requireAuth],
    schema: {
      description: 'Change user password',
      tags: ['Authentication'],
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['currentPassword', 'newPassword'],
        properties: {
          currentPassword: { type: 'string', minLength: 1 },
          newPassword: { 
            type: 'string', 
            minLength: 8,
            pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$'
          }
        }
      },
      response: {
        200: {
          description: 'Password changed successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' }
              }
            }
          }
        },
        401: responseSchemas.error,
      }
    }
  }, authController.changePassword);

  // Deactivate account (requires authentication)
  fastify.post('/auth/deactivate', {
    preHandler: [requireAuth],
    schema: {
      description: 'Deactivate user account',
      tags: ['Authentication'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          description: 'Account deactivated successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' }
              }
            }
          }
        },
        401: responseSchemas.error,
      }
    }
  }, authController.deactivateAccount);
}
