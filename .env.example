# Server Configuration
PORT=3000
HOST=0.0.0.0
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_database_password
DB_NAME=fus_app_db
DB_CONNECTION_LIMIT=10
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_ISSUER=fus-app-api
JWT_AUDIENCE=fus-app-client

# Mailgun Configuration (for password reset emails)
MAILGUN_API_KEY=your-mailgun-api-key
MAILGUN_DOMAIN=your-mailgun-domain.com
MAILGUN_FROM=<EMAIL>

# Security Configuration
BCRYPT_ROUNDS=12
RESET_TOKEN_EXPIRY=3600000

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=15 minutes

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# Pagination Configuration
DEFAULT_PAGE_LIMIT=10
MAX_PAGE_LIMIT=100

# Frontend URL (for email links)
FRONTEND_URL=http://localhost:3000
