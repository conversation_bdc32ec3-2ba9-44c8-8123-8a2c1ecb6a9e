import { eq, lt, count } from 'drizzle-orm';
import { db } from '../connection.js';
import { jwtBlacklist } from '../schema.js';

/**
 * JWT Blacklist repository for managing invalidated tokens
 */
export class JwtBlacklistRepository {
  /**
   * Add token to blacklist
   * @param {string} token - JWT token to blacklist
   * @param {Date} expiresAt - Token expiration date
   * @returns {Promise<boolean>} Success status
   */
  async addToken(token, expiresAt) {
    try {
      await db.insert(jwtBlacklist).values({
        token,
        expiresAt,
      });
      return true;
    } catch (error) {
      console.error('Error adding token to blacklist:', error);
      return false;
    }
  }

  /**
   * Check if token is blacklisted
   * @param {string} token - JWT token to check
   * @returns {Promise<boolean>} Blacklist status
   */
  async isTokenBlacklisted(token) {
    try {
      const [result] = await db.select()
        .from(jwtBlacklist)
        .where(eq(jwtBlacklist.token, token));
      
      return !!result;
    } catch (error) {
      console.error('Error checking token blacklist:', error);
      return false;
    }
  }

  /**
   * Clean up expired tokens from blacklist
   * @returns {Promise<number>} Number of cleaned tokens
   */
  async cleanupExpiredTokens() {
    try {
      const result = await db.delete(jwtBlacklist)
        .where(lt(jwtBlacklist.expiresAt, new Date()));
      
      return result.affectedRows || 0;
    } catch (error) {
      console.error('Error cleaning up expired tokens:', error);
      return 0;
    }
  }

  /**
   * Get blacklist statistics
   * @returns {Promise<Object>} Blacklist stats
   */
  async getStats() {
    try {
      const [{ total }] = await db.select({ 
        total: count() 
      }).from(jwtBlacklist);

      const [{ expired }] = await db.select({ 
        expired: count() 
      }).from(jwtBlacklist).where(lt(jwtBlacklist.expiresAt, new Date()));

      return {
        total: total || 0,
        expired: expired || 0,
        active: (total || 0) - (expired || 0),
      };
    } catch (error) {
      console.error('Error getting blacklist stats:', error);
      return { total: 0, expired: 0, active: 0 };
    }
  }
}
