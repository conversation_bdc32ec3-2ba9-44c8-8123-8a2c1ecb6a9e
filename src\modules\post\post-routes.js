import { Post<PERSON><PERSON>roller } from './post-controller.js';
import { requireAuth, optionalAuthenticate } from '../../core/middleware/auth.js';
import { schemas, responseSchemas } from '../../core/utils/validation.js';

/**
 * Post routes plugin
 * @param {Object} fastify - Fastify instance
 * @param {Object} options - Plugin options
 */
export async function postRoutes(fastify, options) {
  const postController = new PostController();

  // Get all posts with pagination and search
  fastify.get('/posts', {
    preHandler: [optionalAuthenticate],
    schema: {
      description: 'Get all posts with pagination and search',
      tags: ['Posts'],
      querystring: schemas.postQuery.querystring,
      response: {
        200: {
          description: 'Posts retrieved successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: responseSchemas.post
            },
            pagination: responseSchemas.pagination,
            message: { type: 'string' }
          }
        }
      }
    }
  }, postController.getAllPosts);

  // Search posts
  fastify.get('/posts/search', {
    schema: {
      description: 'Search posts',
      tags: ['Posts'],
      querystring: {
        type: 'object',
        required: ['q'],
        properties: {
          q: { type: 'string', minLength: 1, maxLength: 255 },
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
          sortBy: { 
            type: 'string', 
            enum: ['createdAt', 'updatedAt', 'title', 'publishedAt'],
            default: 'createdAt'
          },
          sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'desc' }
        }
      },
      response: {
        200: {
          description: 'Search results retrieved successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: responseSchemas.post
            },
            pagination: responseSchemas.pagination,
            message: { type: 'string' }
          }
        },
        400: responseSchemas.error
      }
    }
  }, postController.searchPosts);

  // Get post statistics
  fastify.get('/posts/stats', {
    schema: {
      description: 'Get post statistics',
      tags: ['Posts'],
      response: {
        200: {
          description: 'Post statistics retrieved successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                total: { type: 'integer' },
                published: { type: 'integer' },
                drafts: { type: 'integer' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, postController.getPostStats);

  // Get current user's posts (requires authentication)
  fastify.get('/posts/me', {
    preHandler: [requireAuth],
    schema: {
      description: 'Get current user posts',
      tags: ['Posts'],
      security: [{ bearerAuth: [] }],
      querystring: schemas.postQuery.querystring,
      response: {
        200: {
          description: 'User posts retrieved successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: responseSchemas.post
            },
            pagination: responseSchemas.pagination,
            message: { type: 'string' }
          }
        },
        401: responseSchemas.error
      }
    }
  }, postController.getUserPosts);

  // Get current user's post statistics (requires authentication)
  fastify.get('/posts/me/stats', {
    preHandler: [requireAuth],
    schema: {
      description: 'Get current user post statistics',
      tags: ['Posts'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          description: 'User post statistics retrieved successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                total: { type: 'integer' },
                published: { type: 'integer' },
                drafts: { type: 'integer' }
              }
            },
            message: { type: 'string' }
          }
        },
        401: responseSchemas.error
      }
    }
  }, postController.getUserPostStats);

  // Get post by ID
  fastify.get('/posts/:id', {
    preHandler: [optionalAuthenticate],
    schema: {
      description: 'Get post by ID',
      tags: ['Posts'],
      params: schemas.idParam.params,
      response: {
        200: {
          description: 'Post retrieved successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: responseSchemas.post,
            message: { type: 'string' }
          }
        },
        404: responseSchemas.error
      }
    }
  }, postController.getPostById);

  // Create new post (requires authentication)
  fastify.post('/posts', {
    preHandler: [requireAuth],
    schema: {
      description: 'Create new post',
      tags: ['Posts'],
      security: [{ bearerAuth: [] }],
      body: schemas.postCreate.body,
      response: {
        201: {
          description: 'Post created successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: responseSchemas.post,
            message: { type: 'string' }
          }
        },
        400: responseSchemas.error,
        401: responseSchemas.error
      }
    }
  }, postController.createPost);

  // Update post by ID (requires authentication and ownership)
  fastify.put('/posts/:id', {
    preHandler: [requireAuth],
    schema: {
      description: 'Update post by ID',
      tags: ['Posts'],
      security: [{ bearerAuth: [] }],
      params: schemas.idParam.params,
      body: schemas.postUpdate.body,
      response: {
        200: {
          description: 'Post updated successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: responseSchemas.post,
            message: { type: 'string' }
          }
        },
        400: responseSchemas.error,
        401: responseSchemas.error,
        403: responseSchemas.error,
        404: responseSchemas.error
      }
    }
  }, postController.updatePost);

  // Toggle publish status (requires authentication and ownership)
  fastify.patch('/posts/:id/publish', {
    preHandler: [requireAuth],
    schema: {
      description: 'Publish or unpublish post',
      tags: ['Posts'],
      security: [{ bearerAuth: [] }],
      params: schemas.idParam.params,
      body: {
        type: 'object',
        required: ['published'],
        properties: {
          published: { type: 'boolean' }
        }
      },
      response: {
        200: {
          description: 'Post publish status updated successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: responseSchemas.post,
            message: { type: 'string' }
          }
        },
        401: responseSchemas.error,
        403: responseSchemas.error,
        404: responseSchemas.error
      }
    }
  }, postController.togglePublishPost);

  // Delete post by ID (requires authentication and ownership)
  fastify.delete('/posts/:id', {
    preHandler: [requireAuth],
    schema: {
      description: 'Delete post by ID',
      tags: ['Posts'],
      security: [{ bearerAuth: [] }],
      params: schemas.idParam.params,
      response: {
        200: {
          description: 'Post deleted successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                message: { type: 'string' }
              }
            }
          }
        },
        401: responseSchemas.error,
        403: responseSchemas.error,
        404: responseSchemas.error
      }
    }
  }, postController.deletePost);

  // Get posts by author ID
  fastify.get('/posts/author/:authorId', {
    schema: {
      description: 'Get posts by author ID',
      tags: ['Posts'],
      params: {
        type: 'object',
        required: ['authorId'],
        properties: {
          authorId: { type: 'integer', minimum: 1 }
        }
      },
      querystring: schemas.postQuery.querystring,
      response: {
        200: {
          description: 'Author posts retrieved successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: responseSchemas.post
            },
            pagination: responseSchemas.pagination,
            message: { type: 'string' }
          }
        }
      }
    }
  }, postController.getPostsByAuthor);
}
