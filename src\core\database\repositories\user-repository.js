import { eq, and } from 'drizzle-orm';
import { db } from '../connection.js';
import { users } from '../schema.js';

/**
 * User repository for database operations
 */
export class UserRepository {
  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Promise<Object>} Created user
   */
  async create(userData) {
    const [result] = await db.insert(users).values(userData);
    return this.findById(result.insertId);
  }

  /**
   * Find user by ID
   * @param {number} id - User ID
   * @returns {Promise<Object|null>} User or null
   */
  async findById(id) {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || null;
  }

  /**
   * Find user by email
   * @param {string} email - User email
   * @returns {Promise<Object|null>} User or null
   */
  async findByEmail(email) {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user || null;
  }

  /**
   * Find user by username
   * @param {string} username - Username
   * @returns {Promise<Object|null>} User or null
   */
  async findByUsername(username) {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || null;
  }

  /**
   * Find user by email or username
   * @param {string} identifier - Email or username
   * @returns {Promise<Object|null>} User or null
   */
  async findByEmailOrUsername(identifier) {
    const [user] = await db.select().from(users).where(
      eq(users.email, identifier) || eq(users.username, identifier)
    );
    return user || null;
  }

  /**
   * Find user by reset password token
   * @param {string} token - Reset password token
   * @returns {Promise<Object|null>} User or null
   */
  async findByResetToken(token) {
    const [user] = await db.select().from(users).where(
      and(
        eq(users.resetPasswordToken, token),
        // Token should not be expired
        // resetPasswordExpires > NOW()
      )
    );
    return user || null;
  }

  /**
   * Update user by ID
   * @param {number} id - User ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object|null>} Updated user or null
   */
  async updateById(id, updateData) {
    await db.update(users).set(updateData).where(eq(users.id, id));
    return this.findById(id);
  }

  /**
   * Update user password reset token
   * @param {string} email - User email
   * @param {string} token - Reset token
   * @param {Date} expires - Token expiration
   * @returns {Promise<boolean>} Success status
   */
  async updateResetToken(email, token, expires) {
    const result = await db.update(users)
      .set({
        resetPasswordToken: token,
        resetPasswordExpires: expires,
      })
      .where(eq(users.email, email));
    
    return result.affectedRows > 0;
  }

  /**
   * Clear reset password token
   * @param {number} id - User ID
   * @returns {Promise<boolean>} Success status
   */
  async clearResetToken(id) {
    const result = await db.update(users)
      .set({
        resetPasswordToken: null,
        resetPasswordExpires: null,
      })
      .where(eq(users.id, id));
    
    return result.affectedRows > 0;
  }

  /**
   * Get public user profile (without sensitive data)
   * @param {string} username - Username
   * @returns {Promise<Object|null>} Public user profile
   */
  async getPublicProfile(username) {
    const [user] = await db.select({
      id: users.id,
      username: users.username,
      firstName: users.firstName,
      lastName: users.lastName,
      bio: users.bio,
      avatar: users.avatar,
      createdAt: users.createdAt,
    }).from(users).where(eq(users.username, username));
    
    return user || null;
  }
}
