# FUS App Backend v2

REST API backend sử dụng Fastify framework với các tính năng authentication, user management, và post management.

## 🚀 Tính năng

### Module Authentication
- ✅ Đăng ký tài khoản mới với validation email và password
- ✅ Đăng nhập với username/email và password, trả về JWT token
- ✅ Gửi email reset password qua MailgunJS
- ✅ Đặt lại mật khẩu với token từ email
- ✅ JWT authentication middleware cho các route cần bảo vệ
- ✅ Logout và invalidate JWT token

### Module User
- ✅ Xem thông tin public của user theo username
- ✅ Lấy thông tin user hiện tại (cần authentication)
- ✅ Cập nhật thông tin user hiện tại (cần authentication)

### Module Post
- ✅ Lấy danh sách tất cả bài viết với pagination và search
- ✅ Xem chi tiết một bài viết
- ✅ Tạo bài viết mới (cần authentication)
- ✅ Chỉnh sửa bài viết của chính mình (cần authentication + ownership check)
- ✅ Xóa bài viết của chính mình (cần authentication + ownership check)

## 🛠️ Tech Stack

- **Framework**: Fastify
- **Database**: MySQL với Drizzle ORM
- **Authentication**: JWT với @fastify/jwt
- **Email**: MailgunJS
- **Validation**: @sinclair/typebox
- **Security**: bcryptjs, @fastify/helmet, @fastify/cors, @fastify/rate-limit

## 📋 Yêu cầu hệ thống

- Node.js >= 16.0.0
- MySQL >= 8.0
- npm hoặc yarn

## 🚀 Cài đặt và chạy

### 1. Clone repository
```bash
git clone <repository-url>
cd fus-app-be-v2
```

### 2. Cài đặt dependencies
```bash
npm install
```

### 3. Cấu hình environment
```bash
cp .env.example .env
```

Chỉnh sửa file `.env` với thông tin cấu hình của bạn:
- Database connection
- JWT secret
- Mailgun credentials (tùy chọn)

### 4. Tạo database
```sql
CREATE DATABASE fus_app_db;
```

### 5. Chạy migrations
```bash
npm run db:generate
npm run db:migrate
```

### 6. Khởi động server
```bash
# Development mode
npm run dev

# Production mode
npm start
```

Server sẽ chạy tại `http://localhost:3000`

## 📚 API Documentation

### Authentication Endpoints

#### POST /auth/register
Đăng ký tài khoản mới

**Request Body:**
```json
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "SecurePass123",
  "firstName": "John",
  "lastName": "Doe"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "username": "johndoe",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "message": "User registered successfully"
}
```

#### POST /auth/login
Đăng nhập

**Request Body:**
```json
{
  "identifier": "johndoe", // username hoặc email
  "password": "SecurePass123"
}
```

#### POST /auth/forgot-password
Gửi email reset password

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

#### POST /auth/reset-password
Đặt lại mật khẩu

**Request Body:**
```json
{
  "token": "reset-token-from-email",
  "password": "NewSecurePass123"
}
```

#### POST /auth/logout
Đăng xuất (cần authentication)

**Headers:**
```
Authorization: Bearer <jwt-token>
```

### User Endpoints

#### GET /users/:username
Xem thông tin public của user

#### GET /users/me
Lấy thông tin user hiện tại (cần authentication)

#### PUT /users/me
Cập nhật thông tin user hiện tại (cần authentication)

**Request Body:**
```json
{
  "firstName": "John Updated",
  "lastName": "Doe Updated",
  "bio": "My updated bio",
  "avatar": "https://example.com/avatar.jpg"
}
```

### Post Endpoints

#### GET /posts
Lấy danh sách bài viết với pagination và search

**Query Parameters:**
- `page`: Trang hiện tại (default: 1)
- `limit`: Số bài viết per page (default: 10, max: 100)
- `search`: Từ khóa tìm kiếm
- `sortBy`: Sắp xếp theo (createdAt, updatedAt, title, publishedAt)
- `sortOrder`: Thứ tự sắp xếp (asc, desc)
- `published`: Lọc theo trạng thái published (true/false)

#### GET /posts/:id
Xem chi tiết bài viết

#### POST /posts
Tạo bài viết mới (cần authentication)

**Request Body:**
```json
{
  "title": "My New Post",
  "content": "This is the content of my post...",
  "excerpt": "Short description",
  "isPublished": true
}
```

#### PUT /posts/:id
Cập nhật bài viết (cần authentication + ownership)

#### DELETE /posts/:id
Xóa bài viết (cần authentication + ownership)

#### GET /posts/search?q=keyword
Tìm kiếm bài viết

#### GET /posts/me
Lấy bài viết của user hiện tại (cần authentication)

## 🔒 Authentication

API sử dụng JWT tokens cho authentication. Để truy cập các protected endpoints:

1. Đăng nhập để lấy JWT token
2. Thêm token vào header của request:
   ```
   Authorization: Bearer <your-jwt-token>
   ```

## 📊 Database Schema

### Users Table
- `id`: Primary key
- `username`: Unique username
- `email`: Unique email
- `password`: Hashed password
- `firstName`, `lastName`: User names
- `bio`: User biography
- `avatar`: Avatar URL
- `isActive`: Account status
- `emailVerified`: Email verification status
- `resetPasswordToken`, `resetPasswordExpires`: Password reset

### Posts Table
- `id`: Primary key
- `title`: Post title
- `content`: Post content
- `excerpt`: Post excerpt
- `slug`: URL-friendly slug
- `authorId`: Foreign key to users
- `isPublished`: Publication status
- `publishedAt`: Publication date
- `createdAt`, `updatedAt`: Timestamps

### JWT Blacklist Table
- `id`: Primary key
- `token`: Blacklisted JWT token
- `expiresAt`: Token expiration
- `createdAt`: Timestamp

## 🧪 Testing

```bash
# Chạy tests
npm test

# Chạy tests với watch mode
npm run test:watch
```

## 🔧 Development

### Database Operations

```bash
# Generate migrations
npm run db:generate

# Run migrations
npm run db:migrate

# Open Drizzle Studio
npm run db:studio
```

### API Documentation

Trong development mode, API documentation có sẵn tại:
`http://localhost:3000/documentation`

## 🚀 Deployment

### Environment Variables cho Production

Đảm bảo set các environment variables sau cho production:

```bash
NODE_ENV=production
JWT_SECRET=your-production-jwt-secret
DB_HOST=your-production-db-host
DB_USER=your-production-db-user
DB_PASSWORD=your-production-db-password
MAILGUN_API_KEY=your-mailgun-api-key
MAILGUN_DOMAIN=your-mailgun-domain
```

### Health Check

API cung cấp health check endpoint tại `/health` để monitor trạng thái:

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "services": {
    "database": {
      "status": "healthy",
      "database": "fus_app_db"
    },
    "email": {
      "status": "healthy",
      "configured": true
    }
  }
}
```

## 📝 License

MIT License

## 🤝 Contributing

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request
