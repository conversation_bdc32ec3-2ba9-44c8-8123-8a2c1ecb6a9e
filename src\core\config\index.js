import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Validate required environment variables
 * @param {string[]} requiredVars - Array of required environment variable names
 * @throws {Error} If any required variable is missing
 */
function validateEnvVars(requiredVars) {
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

/**
 * Application configuration
 */
export const config = {
  // Server configuration
  server: {
    port: parseInt(process.env.PORT) || 3000,
    host: process.env.HOST || '0.0.0.0',
    environment: process.env.NODE_ENV || 'development',
  },

  // Database configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    name: process.env.DB_NAME || 'fus_app_db',
    connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 10,
    acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000,
    timeout: parseInt(process.env.DB_TIMEOUT) || 60000,
  },

  // JWT configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    issuer: process.env.JWT_ISSUER || 'fus-app-api',
    audience: process.env.JWT_AUDIENCE || 'fus-app-client',
  },

  // Mailgun configuration
  mailgun: {
    apiKey: process.env.MAILGUN_API_KEY,
    domain: process.env.MAILGUN_DOMAIN,
    from: process.env.MAILGUN_FROM || '<EMAIL>',
  },

  // Security configuration
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12,
    resetTokenExpiry: parseInt(process.env.RESET_TOKEN_EXPIRY) || 3600000, // 1 hour in ms
  },

  // Rate limiting
  rateLimit: {
    max: parseInt(process.env.RATE_LIMIT_MAX) || 100,
    timeWindow: process.env.RATE_LIMIT_WINDOW || '15 minutes',
  },

  // CORS configuration
  cors: {
    origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['http://localhost:3000'],
    credentials: process.env.CORS_CREDENTIALS === 'true',
  },

  // Pagination defaults
  pagination: {
    defaultLimit: parseInt(process.env.DEFAULT_PAGE_LIMIT) || 10,
    maxLimit: parseInt(process.env.MAX_PAGE_LIMIT) || 100,
  },
};

/**
 * Validate configuration on startup
 */
export function validateConfig() {
  const requiredVars = [];

  // Add required variables based on environment
  if (config.server.environment === 'production') {
    requiredVars.push(
      'JWT_SECRET',
      'DB_HOST',
      'DB_USER',
      'DB_PASSWORD',
      'DB_NAME'
    );

    // Mailgun is required for password reset
    if (!config.mailgun.apiKey || !config.mailgun.domain) {
      console.warn('Warning: Mailgun configuration missing. Password reset will not work.');
    }
  }

  try {
    validateEnvVars(requiredVars);
    console.log('✅ Configuration validation passed');
  } catch (error) {
    console.error('❌ Configuration validation failed:', error.message);
    process.exit(1);
  }
}

/**
 * Get configuration for specific module
 * @param {string} module - Module name
 * @returns {Object} Module configuration
 */
export function getConfig(module) {
  return config[module] || {};
}

/**
 * Check if running in development mode
 * @returns {boolean} Development mode status
 */
export function isDevelopment() {
  return config.server.environment === 'development';
}

/**
 * Check if running in production mode
 * @returns {boolean} Production mode status
 */
export function isProduction() {
  return config.server.environment === 'production';
}

/**
 * Check if running in test mode
 * @returns {boolean} Test mode status
 */
export function isTest() {
  return config.server.environment === 'test';
}
