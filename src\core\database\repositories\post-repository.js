import { eq, and, or, like, desc, asc, count } from 'drizzle-orm';
import { db } from '../connection.js';
import { posts, users } from '../schema.js';

/**
 * Post repository for database operations
 */
export class PostRepository {
  /**
   * Create a new post
   * @param {Object} postData - Post data
   * @returns {Promise<Object>} Created post
   */
  async create(postData) {
    const [result] = await db.insert(posts).values(postData);
    return this.findById(result.insertId);
  }

  /**
   * Find post by ID with author information
   * @param {number} id - Post ID
   * @returns {Promise<Object|null>} Post with author or null
   */
  async findById(id) {
    const [post] = await db.select({
      id: posts.id,
      title: posts.title,
      content: posts.content,
      excerpt: posts.excerpt,
      slug: posts.slug,
      authorId: posts.authorId,
      isPublished: posts.isPublished,
      publishedAt: posts.publishedAt,
      createdAt: posts.createdAt,
      updatedAt: posts.updatedAt,
      author: {
        id: users.id,
        username: users.username,
        firstName: users.firstName,
        lastName: users.lastName,
        avatar: users.avatar,
      }
    })
    .from(posts)
    .leftJoin(users, eq(posts.authorId, users.id))
    .where(eq(posts.id, id));
    
    return post || null;
  }

  /**
   * Find post by slug
   * @param {string} slug - Post slug
   * @returns {Promise<Object|null>} Post or null
   */
  async findBySlug(slug) {
    const [post] = await db.select().from(posts).where(eq(posts.slug, slug));
    return post || null;
  }

  /**
   * Get all posts with pagination and search
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Posts with pagination metadata
   */
  async findAll(options = {}) {
    const {
      page = 1,
      limit = 10,
      search = '',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      authorId = null,
      published = null
    } = options;

    const offset = (page - 1) * limit;
    
    // Build where conditions
    const conditions = [];
    
    if (search) {
      conditions.push(
        or(
          like(posts.title, `%${search}%`),
          like(posts.content, `%${search}%`),
          like(posts.excerpt, `%${search}%`)
        )
      );
    }
    
    if (authorId) {
      conditions.push(eq(posts.authorId, authorId));
    }
    
    if (published !== null) {
      conditions.push(eq(posts.isPublished, published));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;
    
    // Get total count
    const [{ total }] = await db.select({ 
      total: count() 
    }).from(posts).where(whereClause);

    // Get posts with author information
    const orderBy = sortOrder === 'desc' ? desc(posts[sortBy]) : asc(posts[sortBy]);
    
    const postsData = await db.select({
      id: posts.id,
      title: posts.title,
      content: posts.content,
      excerpt: posts.excerpt,
      slug: posts.slug,
      authorId: posts.authorId,
      isPublished: posts.isPublished,
      publishedAt: posts.publishedAt,
      createdAt: posts.createdAt,
      updatedAt: posts.updatedAt,
      author: {
        id: users.id,
        username: users.username,
        firstName: users.firstName,
        lastName: users.lastName,
        avatar: users.avatar,
      }
    })
    .from(posts)
    .leftJoin(users, eq(posts.authorId, users.id))
    .where(whereClause)
    .orderBy(orderBy)
    .limit(limit)
    .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return {
      posts: postsData,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      }
    };
  }

  /**
   * Update post by ID
   * @param {number} id - Post ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object|null>} Updated post or null
   */
  async updateById(id, updateData) {
    await db.update(posts).set(updateData).where(eq(posts.id, id));
    return this.findById(id);
  }

  /**
   * Delete post by ID
   * @param {number} id - Post ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteById(id) {
    const result = await db.delete(posts).where(eq(posts.id, id));
    return result.affectedRows > 0;
  }

  /**
   * Check if user owns the post
   * @param {number} postId - Post ID
   * @param {number} userId - User ID
   * @returns {Promise<boolean>} Ownership status
   */
  async isOwner(postId, userId) {
    const [post] = await db.select({ authorId: posts.authorId })
      .from(posts)
      .where(eq(posts.id, postId));
    
    return post && post.authorId === userId;
  }

  /**
   * Get posts by author
   * @param {number} authorId - Author ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Posts with pagination
   */
  async findByAuthor(authorId, options = {}) {
    return this.findAll({ ...options, authorId });
  }
}
