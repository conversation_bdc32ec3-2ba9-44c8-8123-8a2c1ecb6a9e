import { AuthService } from './auth-service.js';
import { createSuccessResponse } from '../../core/utils/helpers.js';
import { asyncHandler } from '../../core/utils/errors.js';

/**
 * Authentication controller
 */
export class AuthController {
  constructor() {
    this.authService = new AuthService();
  }

  /**
   * Register a new user
   * POST /auth/register
   */
  register = asyncHandler(async (request, reply) => {
    const result = await this.authService.register(request.body, request.server);
    
    reply.code(201).send(createSuccessResponse(result, 'User registered successfully'));
  });

  /**
   * Login user
   * POST /auth/login
   */
  login = asyncHandler(async (request, reply) => {
    const result = await this.authService.login(request.body, request.server);
    
    reply.send(createSuccessResponse(result, 'Login successful'));
  });

  /**
   * Initiate password reset
   * POST /auth/forgot-password
   */
  forgotPassword = asyncHandler(async (request, reply) => {
    const result = await this.authService.forgotPassword(request.body.email);
    
    reply.send(createSuccessResponse(result));
  });

  /**
   * Reset password with token
   * POST /auth/reset-password
   */
  resetPassword = asyncHandler(async (request, reply) => {
    const result = await this.authService.resetPassword(request.body);
    
    reply.send(createSuccessResponse(result));
  });

  /**
   * Logout user
   * POST /auth/logout
   */
  logout = asyncHandler(async (request, reply) => {
    const result = await this.authService.logout(request.token);
    
    reply.send(createSuccessResponse(result));
  });

  /**
   * Get current user profile
   * GET /users/me
   */
  getCurrentUser = asyncHandler(async (request, reply) => {
    const user = await this.authService.getCurrentUser(request.user.id);
    
    reply.send(createSuccessResponse(user));
  });

  /**
   * Update current user profile
   * PUT /users/me
   */
  updateCurrentUser = asyncHandler(async (request, reply) => {
    const user = await this.authService.updateCurrentUser(request.user.id, request.body);
    
    reply.send(createSuccessResponse(user, 'Profile updated successfully'));
  });

  /**
   * Get public user profile
   * GET /users/:username
   */
  getPublicUserProfile = asyncHandler(async (request, reply) => {
    const user = await this.authService.getPublicUserProfile(request.params.username);
    
    reply.send(createSuccessResponse(user));
  });

  /**
   * Change user password
   * POST /auth/change-password
   */
  changePassword = asyncHandler(async (request, reply) => {
    const result = await this.authService.changePassword(request.user.id, request.body);
    
    reply.send(createSuccessResponse(result));
  });

  /**
   * Deactivate user account
   * POST /auth/deactivate
   */
  deactivateAccount = asyncHandler(async (request, reply) => {
    const result = await this.authService.deactivateAccount(request.user.id);
    
    reply.send(createSuccessResponse(result));
  });
}
